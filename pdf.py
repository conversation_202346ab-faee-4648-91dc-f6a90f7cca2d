#!/usr/bin/env python3
"""
PDF Text and Image Extractor

This script extracts text and images from a PDF file and saves them to an output folder.
Usage: python pdf.py input.pdf

Requirements:
- PyPDF2 or pypdf for text extraction
- Pillow (PIL) for image processing
- fitz (PyMuPDF) for comprehensive PDF processing

The script will create an output folder with:
- extracted_text.txt: All text content from the PDF
- image_001.png, image_002.png, etc.: All images found in the PDF
"""

import sys
import os
import argparse
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_dependencies():
    """Check if required dependencies are installed."""
    missing_deps = []

    try:
        import fitz  # PyMuPDF
    except ImportError:
        missing_deps.append("PyMuPDF")

    try:
        from PIL import Image
    except ImportError:
        missing_deps.append("Pillow")

    if missing_deps:
        logger.error(f"Missing dependencies: {', '.join(missing_deps)}")
        logger.error("Please install them using:")
        logger.error("pip install PyMuPDF Pillow")
        return False

    return True

def create_output_directory(pdf_path):
    """Create output directory based on PDF filename."""
    pdf_name = Path(pdf_path).stem
    output_dir = Path(f"{pdf_name}_extracted")
    output_dir.mkdir(exist_ok=True)
    return output_dir

def extract_text_from_pdf(pdf_path, output_dir):
    """Extract text from PDF and save to text file."""
    try:
        import fitz  # PyMuPDF

        logger.info("Extracting text from PDF...")
        doc = fitz.open(pdf_path)
        text_content = []

        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            text = page.get_text()
            if text.strip():
                text_content.append(f"--- Page {page_num + 1} ---\n")
                text_content.append(text)
                text_content.append("\n\n")

        doc.close()

        # Save extracted text
        text_file = output_dir / "extracted_text.txt"
        with open(text_file, 'w', encoding='utf-8') as f:
            f.writelines(text_content)

        logger.info(f"Text extracted and saved to: {text_file}")
        return len([page for page in text_content if page.startswith("--- Page")])

    except Exception as e:
        logger.error(f"Error extracting text: {e}")
        return 0

def extract_images_from_pdf(pdf_path, output_dir):
    """Extract images from PDF and save as PNG files."""
    try:
        import fitz  # PyMuPDF
        from PIL import Image

        logger.info("Extracting images from PDF...")
        doc = fitz.open(pdf_path)
        image_count = 0

        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            image_list = page.get_images()

            for img_index, img in enumerate(image_list):
                try:
                    # Get image data
                    xref = img[0]
                    pix = fitz.Pixmap(doc, xref)

                    # Convert to PIL Image if needed
                    if pix.n - pix.alpha < 4:  # GRAY or RGB
                        img_data = pix.tobytes("png")
                        image_count += 1

                        # Save image
                        img_filename = output_dir / f"image_{image_count:03d}.png"
                        with open(img_filename, "wb") as img_file:
                            img_file.write(img_data)

                        logger.info(f"Saved image: {img_filename}")
                    else:  # CMYK: convert to RGB first
                        pix1 = fitz.Pixmap(fitz.csRGB, pix)
                        img_data = pix1.tobytes("png")
                        image_count += 1

                        # Save image
                        img_filename = output_dir / f"image_{image_count:03d}.png"
                        with open(img_filename, "wb") as img_file:
                            img_file.write(img_data)

                        logger.info(f"Saved image: {img_filename}")
                        pix1 = None

                    pix = None

                except Exception as e:
                    logger.warning(f"Could not extract image {img_index} from page {page_num + 1}: {e}")
                    continue

        doc.close()
        logger.info(f"Total images extracted: {image_count}")
        return image_count

    except Exception as e:
        logger.error(f"Error extracting images: {e}")
        return 0

def main():
    """Main function to process PDF file."""
    parser = argparse.ArgumentParser(description="Extract text and images from PDF files")
    parser.add_argument("pdf_file", help="Path to the PDF file to process")
    parser.add_argument("--text-only", action="store_true", help="Extract only text")
    parser.add_argument("--images-only", action="store_true", help="Extract only images")

    args = parser.parse_args()

    # Check if dependencies are installed
    if not check_dependencies():
        sys.exit(1)

    # Validate input file
    pdf_path = Path(args.pdf_file)
    if not pdf_path.exists():
        logger.error(f"PDF file not found: {pdf_path}")
        sys.exit(1)

    if not pdf_path.suffix.lower() == '.pdf':
        logger.error(f"File is not a PDF: {pdf_path}")
        sys.exit(1)

    # Create output directory
    output_dir = create_output_directory(pdf_path)
    logger.info(f"Output directory: {output_dir}")

    # Extract content based on options
    text_pages = 0
    image_count = 0

    if not args.images_only:
        text_pages = extract_text_from_pdf(pdf_path, output_dir)

    if not args.text_only:
        image_count = extract_images_from_pdf(pdf_path, output_dir)

    # Summary
    logger.info("=" * 50)
    logger.info("EXTRACTION COMPLETE")
    logger.info(f"PDF file: {pdf_path}")
    logger.info(f"Output directory: {output_dir}")
    if not args.images_only:
        logger.info(f"Text pages processed: {text_pages}")
    if not args.text_only:
        logger.info(f"Images extracted: {image_count}")
    logger.info("=" * 50)

if __name__ == "__main__":
    main()